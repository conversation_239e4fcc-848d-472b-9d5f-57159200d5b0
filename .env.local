PORT=3000
WORKER_CONCURRENCY=64

REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_CLUSTER_MODE=false

MONGO_HOST=localhost
MONGO_USER=test
MONGO_PASSWORD=test

ROBOT_CONFIG={"wpkHttpURL": "http://**************/wepoker", "platform" : "dev"}
ROBOT_CONFIG_WPK_URL=http://**************/wepoker
ROBOT_CONFIG_WPK_PLATFORM=dev

WPTGO_URL=https://api.stg.wptg.a5-labs-cloud.com
WPTGO_WS_URL=wss://wptg-gate-stg.a5labsapp.co
WPTGO_APP_BUNDLE_ID=com.wptasia.wpt

# If you don't want to output some logging tags - enumerate them separated by ,
# NO_LOG_TAGS="NO_LOG_TAGS=JOB_PROGRESS, STRATEGY_SERVICE_CALL"

STRATEGY_SERVICE_URL=http://gto-glue-svc.priv.dev.wpto.a5-labs-cloud.com # DEV
# STRATEGY_SERVICE_URL=http://gto-glue-svc.priv.stg.wptg.a5-labs-cloud.com # STAGING

MTT_CONFIG_MTTWORLD=ws://*************:3001
MTT_CONFIG_MTTGAME=ws://*************:4001
MTT_CONFIG_MTTAPI=http://*************:22001
MTT_PROTOBUF_VERSION=v2

# UNLEASH_API_URL=https://unleash.dev.fungamer.io/api
# UNLEASH_API_TOKEN=default:development.f01957ac1dc0b92ba4ec60e12cefb0d1c68f1e7ce308598fc6452f3b
