import BigNumber from 'bignumber.js';
import cv from '../cv';

enum eBigNumberRoundingMode {
    /**
     * 向远离零的方向舍入(若舍入位为非零，则对舍入部分的前一位数字加1；若舍入位为零，则直接舍弃, 向外取整模式)
     */
    ROUND_UP = 0,

    /**
     * 向接近零的方向舍入(不论舍入位是否为零，都直接舍弃, 向内取整模式)
     */
    ROUND_DOWN = 1,

    /**
     * 若 decimalPlaces 为正，则舍入行为与 ROUND_UP 相同; 若为负，则舍入行为与 ROUND_DOWN 相同(向上取整模式)
     */
    ROUND_CEIL = 2,

    /**
     * 若 decimalPlaces 为正，则舍入行为与 ROUND_DOWN 相同; 若为负，则舍入行为与 ROUND_UP 相同(向下取整模式)
     */
    ROUND_FLOOR = 3,

    /**
     * 四舍五入
     */
    ROUND_HALF_UP = 4,

    /**
     * 五舍六入
     */
    ROUND_HALF_DOWN = 5,

    /**
     * 整数位若是奇数则四舍五入，若是偶数则五舍六入(银行家舍入模式)
     */
    ROUND_HALF_EVEN = 6,

    /**
     * 舍入位 >= 5 与 ROUND_CEIL 相同, 否则与 ROUND_FLOOR 相同
     */
    ROUND_HALF_CEIL = 7,

    /**
     * 舍入位 >= 6 与 ROUND_CEIL 相同, 否则与 ROUND_FLOOR 相同
     */
    ROUND_HALF_FLOOR = 8,
}

/**
 * 字符串工具类
 */
export class StringTools {
    private static _g_instance: StringTools = null; // 单例

    /**
     * 获取单例
     */
    static getInstance(): StringTools {
        if (!StringTools._g_instance) {
            StringTools._g_instance = new StringTools();
        }
        return StringTools._g_instance;
    }

    /**
     * 获取数组或者string的长度，添加了为null的情况
     * @param arr 要处理的数组
     */
    getArrayLength(value: any): number {
        let retVal: number = 0;
        if (value !== null && typeof value !== 'undefined') {
            retVal = cv.Number(value.length);
        }
        return retVal;
    }

    /**
     * 字符串格式化, 支持变长参数(更高级的用法请参照下面的 formatC 接口)
     * @param str       格式
     * @param args      参数
     * @example console.log(StringTools.format("{0}, {1}", 1, 2));   // "1, 2"
     * @example console.log(StringTools.format("i am {0}, test"));   // "i am test"
     */
    format(str: string, ...args: any[]): string {
        if (str === null || str === undefined) return null;

        // 遍历替换
        let result = str;
        for (let i = 0; i < args.length; ++i) {
            result = result.replace('{' + i + '}', args[i]);
        }

        return result;
    }

    /**
     * C风格的字符串格式化(用法和VC++的CString::Format一致, 这里暂只实现几种常用的格式转换)
     * @borrows 字符 意义
     * @borrows a   浮点数、十六进制数字和p-计数法(C99)
     * @borrows A   浮点数、十六进制数字和p-计数法(C99)
     * @borrows c   输出单个字符
     * @borrows d   以十进制形式输出带符号整数(正数不输出符号)
     * @borrows e   以指数形式输出单、双精度实数
     * @borrows E   以指数形式输出单、双精度实数
     * @borrows f   以小数形式输出单、双精度实数
     * @borrows g   以%f%e中较短的输出宽度输出单、双精度实数,%e格式在指数小于-4或者大 于等于精度时使用
     * @borrows G   以%f%e中较短的输出宽度输出单、双精度实数,%e格式在指数小于-4或者大于等于精度时使用
     * @borrows i   有符号十进制整数(与%d相同)
     * @borrows o   以八进制形式输出无符号整数(不输出前缀O)
     * @borrows p   指针
     * @borrows s   输出字符串
     * @borrows x   以十六进制形式输出无符号整数(不输出前缀OX)
     * @borrows X   以十六进制形式输出无符号整数(不输出前缀OX)
     * @borrows u   以十进制形式输出无符号整数
     */
    formatC(str: string, ...arr: any[]): string {
        let i = 0;
        let callback = function (exp, sign, min, precision, attach, type) {
            if (i < 0 || i >= arr.length) return;

            let preLen = !precision ? precision : parseInt(precision.substr(1));
            let val = exp;
            let matchTypeOk = true;

            let t = attach + type;
            switch (t) {
                case 's':
                case 'S':
                    val = cv.String(arr[i]);
                    break;

                case 'c':
                    val = cv.String(arr[i])[0];
                    break;
                case 'C':
                    val = cv.String(arr[i]).toUpperCase()[0];
                    break;

                case 'u':
                case 'U':
                    val = Math.floor(cv.Number(arr[i])).toString(10);
                    break;

                case 'd':
                case 'D':
                    val = Math.floor(cv.Number(arr[i])).toString(10);
                    break;

                case 'o':
                    val = Math.floor(cv.Number(arr[i])).toString(8).toLowerCase();
                    break;
                case 'O':
                    val = Math.floor(cv.Number(arr[i])).toString(8).toUpperCase();
                    break;

                case 'x':
                    val = Math.floor(cv.Number(arr[i])).toString(16).toLowerCase();
                    break;
                case 'X':
                    val = Math.floor(cv.Number(arr[i])).toString(16).toUpperCase();
                    break;

                case 'f':
                case 'F':
                    val = preLen
                        ? parseFloat(cv.Number(arr[i]).toString()).toFixed(preLen)
                        : parseFloat(cv.Number(arr[i]).toString());
                    break;

                case 'p':
                case 'P':
                    val = preLen
                        ? parseFloat(cv.Number(arr[i]).toString()).toPrecision(preLen)
                        : parseFloat(cv.Number(arr[i]).toString());
                    break;

                case 'e':
                case 'E':
                    val = preLen
                        ? parseFloat(cv.Number(arr[i]).toString()).toExponential(preLen)
                        : parseFloat(cv.Number(arr[i]).toString());
                    break;

                case 'ld':
                case 'LD':
                    val = Math.floor(cv.Number(arr[i])).toString(10);
                    break;

                case 'lld':
                case 'LLD':
                    val = Math.floor(cv.Number(arr[i])).toString(10);
                    break;

                case 'lf':
                case 'LF':
                    val = preLen
                        ? parseFloat(cv.Number(arr[i]).toString()).toFixed(preLen)
                        : parseFloat(cv.Number(arr[i]).toString());
                    break;

                default:
                    matchTypeOk = false;
                    break;
            }

            if (matchTypeOk && min) {
                let sz = Math.floor(Number(min));
                let ch = min && min[0] === '0' ? '0' : ' ';
                while (val.length < sz) val = sign ? val + ch : ch + val;
            }

            ++i;
            return val;
        };

        // C语言中格式字符串的一般形式为: %[标志][输出最小宽度][.精度][长度][附加的'l*'字段]类型, 其中方括号[]中的项为可选项
        let regex: RegExp = /%(-)?(0?[0-9]+)?([.][0-9]+)?(l*)([scudoxfpe])/gi;

        // 开始匹配
        return str.replace(regex, callback);
    }

    /**
     * 随机生成 区间: [min, max) 的一个随机数(不一定是整数, 这里未做整数处理)
     * @param min       最小区间
     * @param max       最大区间
     */
    randomRange(min: number, max: number): number {
        return min + Math.random() * (max - min);
    }

    /**
     * 精确乘法 a * b
     */
    times(a: number, b: number): number {
        let value: number = 0;

        // BigNumber
        let bn_a: BigNumber = new BigNumber(a);
        let bn_b: BigNumber = new BigNumber(b);
        value = bn_a.times(bn_b).toNumber();

        // // mathjs
        // let result: MathType = multiply(a, b);
        // value = cv.Number(result);

        // // number-precision
        // value = NP.times(a, b);

        return value;
    }

    serverGoldByClient(gold: number): number {
        return this.times(gold, 100);
    }

    clientGoldByServer(gold: number): number {
        return this.times(gold, 0.01);
    }

    deepCopy(srcObj: any, destObj: any): any {
        if (srcObj !== null && typeof srcObj !== 'undefined') {
            destObj = destObj || {};
            for (let fieldName in srcObj) {
                if (!srcObj.hasOwnProperty(fieldName)) continue;
                if (typeof srcObj[fieldName] === 'object') {
                    if (!srcObj[fieldName]) continue;
                    destObj[fieldName] = srcObj[fieldName].constructor === Array ? [] : {};
                    this.deepCopy(srcObj[fieldName], destObj[fieldName]);
                } else {
                    destObj[fieldName] = srcObj[fieldName];
                }
            }
        }
        return destObj;
    }

    /**
     * 客户端金币转显示比例number
     */
    numberToShowNumber(number: number): number {
        return this.times(cv.config.getShowGoldRatio(), number);
    }

    /**
     * 客户端金币转显示比例字符串
     */
    numberToShowString(number: number): string {
        return this.numberToShowNumber(number).toString();
    }

    numToFloatString(num: number): string {
        return this.numberToString(this.clientGoldByServer(num));
    }

    numberToString(number: number): string {
        let fnum = parseFloat(number.toString());
        let result = 0;
        if (number == 0) {
            return '0';
        } else if (number > 0) {
            if (number * 10 - parseInt((fnum * 10).toString()) > 0) {
                result = this.handleNumberByFloor(number, 2);
            } else if (number - parseInt(number.toString()) == 0) {
                result = number;
            } else {
                result = this.handleNumberByFloor(number, 2);
            }
            return this.numberToShowString(result);
        } else {
            number = -number;
            let fnum = parseFloat(number.toString());
            if (number - parseInt((fnum * 10).toString()) > 0) {
                result = this.handleNumberByFloor(number, 2);
            } else if (number - parseInt(number.toString()) == 0) {
                result = number;
            } else {
                result = this.handleNumberByFloor(number, 2);
            }
            return this.numberToShowString(-result);
        }
    }
    RoundingMode = eBigNumberRoundingMode;
    handleNumberByFloor(num: number, index: number): number {
        return this.toFixed(num, index, this.RoundingMode.ROUND_DOWN);
        // return (Math.floor(this.times(num , Math.pow(10, index))) * Math.pow(0.1, index)).toFixed(index);
    }

    toFixed(
        value: number,
        decimalPlaces: number = 2,
        roundingMode: BigNumber.RoundingMode = BigNumber.ROUND_HALF_UP,
    ): number {
        value = cv.Number(value);
        decimalPlaces = cv.Number(decimalPlaces);

        let nRet: number = 0;
        let nBigNum: BigNumber = new BigNumber(value);
        let strBigNum: string = nBigNum.toFixed(decimalPlaces, roundingMode);

        // strBigNum 会保留小数点末尾多余的0, 转为 number 则自动舍弃了小数点末尾多余的0
        nRet = cv.Number(strBigNum);

        return nRet;
    }

    clearArray(arr: any[]): void {
        if (Array.isArray(arr)) {
            arr.splice(0, arr.length);
        }
    }

    /**
     * 显示比例转客户端金币number
     */
    showStringToNumber(str: string): number {
        return this.div(cv.Number(parseFloat(str)), cv.config.getShowGoldRatio());
    }

    div(a: number, b: number): number {
        let value: number = 0;

        // BigNumber
        let bn_a: BigNumber = new BigNumber(a);
        let bn_b: BigNumber = new BigNumber(b);
        value = bn_a.div(bn_b).toNumber();

        return value;
    }
    /**
     * 服务器金币转显示比例number
     */
    serverGoldToShowNumber(number: number): number {
        return this.numberToShowNumber(this.clientGoldByServer(number));
    }

    /**
     * 服务器金币转显示比例字符串
     */
    serverGoldToShowString(number: number): string {
        return this.serverGoldToShowNumber(number).toString();
    }

    /**
     * 获取带符号的数字字符串( >0 返回 "+0" ; < 0 返回:"-0" )
     */
    getSignString(value: number): string {
        value = cv.Number(value);
        let sRet: string = cv.String(value);
        if (value > 0) sRet = '+' + sRet;
        return sRet;
    }
}
