import { logging } from 'shared';
import { weightedRandomSelection } from 'shared/src/strategy';

export const LOW_OUTS_COUNT = 3;
export const MEDIUM_OUTS_COUNT = 7;
export const HIGH_OUTS_COUNT = 12;

export const LOW_COVERAGE_AMOUNT = 12;
export const MEDIUM_LOW_COVERAGE_AMOUNT = 20;
export const MEDIUM_HIGH_COVERAGE_AMOUNT = 33;
export const BREAK_EAVEN_COVERAGE_AMOUNT = 50;
export const FULL_POT_COVERAGE_AMOUNT = 100;

export const calculateInsuranceAmount = (outsCount: number) => {
    const shouldBuy = shouldBuyInsurance(outsCount);

    if (!shouldBuy) {
        return null;
    }

    const coveregeAmountsWaights = getCoverageAmountWeights(outsCount);
    const coverageAmount = weightedRandomSelection(
        coveregeAmountsWaights,
        (entry) => entry.weight,
    ).covarageAmount;

    logging.withTag('INSURANCE_DECISION').info('Insurance decision made', {
        outsCount,
        shouldBuy,
        amount: coverageAmount,
    });

    return coverageAmount;
};

const shouldBuyInsurance = (outsCount: number): boolean => {
    let buyProbability: number;

    if (outsCount <= LOW_OUTS_COUNT) {
        buyProbability = 0.1; // Low probability - opponent has few ways to improve
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        buyProbability = 0.25; // Medium-low probability - moderate opponent threat
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        buyProbability = 0.35; // Medium probability - significant opponent threat
    } else {
        buyProbability = 0.55; // High probability - opponent has many ways to improve
    }

    return buyProbability > Math.random();
};

const getCoverageAmountWeights = (outsCount: number) => {
    if (outsCount <= LOW_OUTS_COUNT) {
        // Few opponent outs - lower risk, prefer smaller coverage amounts
        return [
            {
                covarageAmount: LOW_COVERAGE_AMOUNT,
                weight: 50,
            },
            {
                covarageAmount: MEDIUM_LOW_COVERAGE_AMOUNT,
                weight: 35,
            },
            {
                covarageAmount: MEDIUM_HIGH_COVERAGE_AMOUNT,
                weight: 15,
            },
        ];
    } else if (outsCount <= MEDIUM_OUTS_COUNT) {
        // Medium opponent outs - moderate risk, balanced coverage
        return [
            {
                covarageAmount: LOW_COVERAGE_AMOUNT,
                weight: 30,
            },
            {
                covarageAmount: MEDIUM_LOW_COVERAGE_AMOUNT,
                weight: 40,
            },
            {
                covarageAmount: MEDIUM_HIGH_COVERAGE_AMOUNT,
                weight: 20,
            },
            {
                covarageAmount: BREAK_EAVEN_COVERAGE_AMOUNT,
                weight: 10,
            },
        ];
    } else if (outsCount <= HIGH_OUTS_COUNT) {
        // More opponent outs - higher risk, prefer medium to high coverage
        return [
            {
                covarageAmount: MEDIUM_LOW_COVERAGE_AMOUNT,
                weight: 25,
            },
            {
                covarageAmount: MEDIUM_HIGH_COVERAGE_AMOUNT,
                weight: 35,
            },
            {
                covarageAmount: BREAK_EAVEN_COVERAGE_AMOUNT,
                weight: 30,
            },
            {
                covarageAmount: FULL_POT_COVERAGE_AMOUNT,
                weight: 10,
            },
        ];
    } else {
        // Many opponent outs - high risk, prefer higher coverage amounts
        return [
            {
                covarageAmount: MEDIUM_HIGH_COVERAGE_AMOUNT,
                weight: 20,
            },
            {
                covarageAmount: BREAK_EAVEN_COVERAGE_AMOUNT,
                weight: 40,
            },
            {
                covarageAmount: FULL_POT_COVERAGE_AMOUNT,
                weight: 40,
            },
        ];
    }
};
