import cv from '../cv';
import { BitHandler } from '../tools/BitHandler';
import { ByteArray } from '../tools/ByteArray';
import { aesHandler } from '../tools/aesHandler';
import WebSocket, { CloseEvent, ErrorEvent } from 'ws';

import { HttpsProxyAgent } from 'https-proxy-agent';
import { logging } from 'shared';

import { protocol as game_pb } from '../../proto/gs_protocol';

const RECONNECT_DELAY = 3000;
let reconnectAttempts = 0;

enum CONNECTION_STATUS {
    DISCONNECTED = 0,
    CONNECTING = 1,
    CONNECTED = 2,
    STOPPED = 3,
}

export class NetWork {
    public wSocket: WebSocket = null;
    public u32seq: number = null;
    public handlers = new Map<number, Map<number, Function>>();
    private headLen: number = 20;
    public static instance: NetWork;
    private _xorValue: number = 0;

    public proxyUrl: string;
    public lastOnMessageCall: number = 0;

    private connectionStatus: CONNECTION_STATUS = CONNECTION_STATUS.DISCONNECTED;

    constructor(proxyUrl: string = null) {
        this.proxyUrl = proxyUrl;
    }

    public static getInstance(proxyUrl?: string): NetWork {
        if (!this.instance) {
            this.instance = new NetWork(proxyUrl);
        }
        return this.instance;
    }

    public connect(): void {
        const name = 'PKW.NetWork.connectServer';
        logging.withTag('WEBSOCKET').info(`${name}: connecting to server`);
        if (this.connectionStatus === CONNECTION_STATUS.CONNECTING) {
            logging.withTag('WEBSOCKET').info(`${name}: is already connecting`);
            return;
        }
        this.close();

        const urlData = cv.domainMgr.getServerInfo();

        if (!urlData || !urlData.gate_server) {
            logging.error(`${name}: No gate server URL found in domain manager`, { urlData });
            return;
        }

        this.connectionStatus = CONNECTION_STATUS.CONNECTING;
        this.initWebSocket(urlData.gate_server);
    }

    private initWebSocket(url: string): void {
        const name = 'PKW.NetWork.initWebSocket';

        if (this.proxyUrl) {
            const agent = new HttpsProxyAgent(this.proxyUrl);
            this.wSocket = new WebSocket(url, { agent });
            logging.info(`${name}: Initialized websocket connection with proxy`, { proxyUrl: this.proxyUrl, url });
        } else {
            this.wSocket = new WebSocket(url);
            logging.info(`${name}: Initialized websocket connection without proxy`, { url });
        }

        this.wSocket.binaryType = 'arraybuffer';
        this.u32seq = 0;

        const connectionInitTimeout = setTimeout(() => {
            this.close();

            throw new Error(`${name}: Unable to open websocket connection within the timeout`);
        }, 5000);


        this.wSocket.onmessage = this.onmessage;

        this.wSocket.onopen = () => {
            this.connectionStatus = CONNECTION_STATUS.CONNECTED;
            logging.info(`${name}: connected to websocket`);
            if (reconnectAttempts > 0) {
                logging.warn(`${name}: reconnected to websocket, attempt ${reconnectAttempts}`);
            }
            clearTimeout(connectionInitTimeout);
            cv.worldNet.onConnectOpen();
        };

        this.wSocket.onerror = (event: ErrorEvent) => {
            clearTimeout(connectionInitTimeout);
            logging.error(`${name}: websocket error`, event.error, { ...event, target: null });
        };

        this.wSocket.onclose = (event: CloseEvent) => {
            clearTimeout(connectionInitTimeout);
            logging.warn(`${name}: websocket closed, will reconnect`, { ...event, target: null });

            this.close();
            cv.netWorkManager.closeGameConnect(true);

            if (this.connectionStatus !== CONNECTION_STATUS.STOPPED) {
                this.connectionStatus = CONNECTION_STATUS.DISCONNECTED;
                reconnectAttempts++;
                setTimeout(() => this.connect(), RECONNECT_DELAY * reconnectAttempts);
                logging.info(`${name}: Reconnecting to server in ${RECONNECT_DELAY * reconnectAttempts} ms`);
            }
        };
    }

    //! 消息注册
    public registerMsg(serverId: number, msgid: number, fn: any): void {
        let value: Map<number, Function> = this.handlers.get(serverId);
        if (!value) {
            this.handlers.set(serverId, new Map());
            value = this.handlers.get(serverId);
        }
        // if (value.get(msgid)) {
        //     // console.log("msgid:" + msgid + "' handler has been registered.");
        //     return;
        // }
        value.set(msgid, fn);

        // console.log("register:msgid: " + msgid);
    }

    /**
     * 清理GameId的所有消息注册
     * @param serverId 对应的GameId
     */
    public unregisterMsgForGame(serverId: number) {
        let value: Map<number, Function> = this.handlers.get(serverId);
        if (value) {
            value.clear();
            this.handlers.delete(serverId);
        }
    }

    public sendMsg(pbbuf: Buffer, msgid: number, Roomid: number, ServerType: number, ServerId: number): boolean {
        if (this.sendPackage(pbbuf, msgid, Roomid, ServerType, ServerId)) {
            logging.setLastActionTaken(game_pb.MSGID[msgid]);
            return true;
        } else {
            return false;
        }
    }

    /**
     * Message packet
     * @param ServerType server type SeverType_World, SeverType_Game
     */
    public sendPackage(
        pbbuf: Buffer,
        msgid: number,
        Roomid: number,
        ServerType: number,
        ServerId: number,
    ): boolean {
        let entryptStr;
        let isEncrypt: boolean = this.isEncrypt(ServerId);
        if (isEncrypt) {
            entryptStr = aesHandler.EncryptBytes(pbbuf);
        }
        let burffer = new ByteArray();
        burffer.createBuffer(1024);
        let u16PackLen = this.headLen + (isEncrypt ? entryptStr.length : pbbuf == null ? 0 : pbbuf.byteLength);
        let u16Msgid = msgid;
        let u32playerid;
        if (ServerId == cv.Enum.GameId.PokerMaster || ServerId == cv.Enum.GameId.CowBoy) {
            // u32playerid = (<any>window).CurrentUserInfo.user.wasUserInDiamondGame
            //     ? (<any>window).CurrentUserInfo.user.userId
            //     : cv.dataHandler.getUserData().user_id;
        } else {
            u32playerid = cv.dataHandler.getUserData().user_id;
        }
        let u32roomid = Roomid;
        let u16serverType = ServerType;
        let u16serverId = ServerId;
        burffer.writeUint16(u16serverType);
        burffer.writeUint16(u16serverId);
        burffer.writeUint16(u16PackLen);
        burffer.writeUint16(u16Msgid);
        burffer.writeUint32(this.u32seq);
        burffer.writeUint32(u32playerid);
        burffer.writeUint32(u32roomid);

        burffer.writeBuffer(isEncrypt ? entryptStr : pbbuf);
        burffer.wpos = u16PackLen;
        if (ServerId == cv.Enum.GameId.PokerMaster) {
            //    return (<any>window).CurrentUserInfo.user.wasUserInDiamondGame? PokerMasterNetWork.getInstance().send(burffer.getbuffer()):this.send(burffer.getbuffer());
        } else if (ServerId == cv.Enum.GameId.CowBoy) {
            //     return (<any>window).CurrentUserInfo.user.wasUserInDiamondGame? CowboyWsNetWork.getInstance().send(burffer.getbuffer()):this.send(burffer.getbuffer());
        } else {
            return this.send(burffer.getbuffer());
        }
    }

    //! 消息发送
    public send(data): boolean {
        if (!this.wSocket) return false;
        // console.log("=====> wSocket.readyState  1111= " + this.wSocket.readyState);
        // cc.log("=====> wSocket.readyState  1111= " + this.wSocket.readyState);
        // console.log("Saad request is ");
        // console.log(data);
        if (this.wSocket.readyState === WebSocket.OPEN) {
            this.wSocket.send(data);
            this.u32seq += 1;
            return true;
        }
        return false;
    }

    //数据操作
    //opType: 操作类型 0、对指定数据进行异或操作 1、数据翻转 2、相邻两位数据互换 3、数据取反
    //bitSize: 当前数据操作位数 16 or 32
    //value： 操作数据值
    private getValueByOp(opType: number, bitSize: number, value: number): number {
        let _xorValue = this._xorValue;
        if (opType == 0) {
            //不操作
            if (bitSize == 8) {
                return (value ^ _xorValue) & 0xff;
            } else if (bitSize == 16) {
                return (value ^ _xorValue) & 0xffff;
            } else if (bitSize == 32) {
                return value ^ _xorValue;
            }
        } else if (opType == 1) {
            //数据位翻转
            return BitHandler.reverse_bits(value, bitSize);
        } else if (opType == 2) {
            // 数据位相邻两位互换
            if (bitSize == 8) {
                return BitHandler.swapoddeven_8bits(value);
            } else if (bitSize == 16) {
                return BitHandler.swapoddeven_16bits(value);
            } else if (bitSize == 32) {
                return BitHandler.swapoddeven_32bits(value);
            }
        } else if (opType == 3) {
            //对数据进行取反

            if (bitSize == 8) {
                return ~value & 0xff;
            } else if (bitSize == 16) {
                return ~value & 0xffff;
            } else if (bitSize == 32) {
                return ~value;
            }
        }
    }

    //解析数据协议
    //buffer:ByteArray操作对象
    //policyData1: 数据协议的前32位
    //policyData2: 数据头协议的后32位
    private parsePolicyData(buffer: ByteArray, policyData1: number, policyData2: number): number[] {
        //读取协议位
        let bitExtent = 32;
        let offset = 0;

        let retArray: number[] = [];
        let _MsgHeadMap = new Map<number, number>();

        let _curPlicyData = policyData1;

        let msgHeaderFlag = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, 8); //读取8位协议位
        offset += 8;
        if (msgHeaderFlag != 0x8c && msgHeaderFlag != 0x7a) {
            // console.log("Error: parsePolicyData error. unknow msgHeaderFlag:" + msgHeaderFlag);
            return retArray;
        }

        if (msgHeaderFlag == 0x7a) {
            //如果操作值是0x7a 表示按照之前逻辑操作
            let U32serverType = buffer.readUint16(); //服务器类型
            retArray[0] = U32serverType;
            let U32serverid = buffer.readUint16(); //服务器ID
            retArray[1] = U32serverid;
            let u16PackLen = buffer.readUint16();
            retArray[2] = u16PackLen;
            let u16Msgid = buffer.readUint16();
            retArray[3] = u16Msgid;
            let u32seq = buffer.readUint32();
            retArray[4] = u32seq;
            let U32playerid = buffer.readUint32();
            retArray[5] = U32playerid;
            let U32roomid = buffer.readUint32();
            retArray[6] = U32roomid;
            return retArray;
        }

        //// console.log("###################policyData1 2 :" + policyData1.toString(2));
        //// console.log("###################policyData2 2 :" + policyData2.toString(2));
        let msgBitLen = BitHandler.getReadMidNumFromByte(_curPlicyData, bitExtent, offset, offset + 4);
        offset += 4;

        for (let i = 0; i < 7; i++) {
            let msgType = 0;
            let msgValue = 0;

            if (bitExtent - offset < msgBitLen) {
                //如果当前位数不够了
                let _remainBit = BitHandler.readRightBitFromByte(
                    _curPlicyData,
                    bitExtent,
                    bitExtent - offset,
                );
                _curPlicyData = policyData2;
                let _feedBitLen = msgBitLen - (bitExtent - offset);
                let _feedBit = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, _feedBitLen);
                msgType = BitHandler.concatBinaryNumber(_remainBit, _feedBit, _feedBitLen);
                offset = _feedBitLen;
            } else {
                msgType = BitHandler.getReadMidNumFromByte(
                    _curPlicyData,
                    bitExtent,
                    offset,
                    offset + msgBitLen,
                );
                offset += msgBitLen;
            }

            if (bitExtent - offset < 2) {
                //如果当前位数不够了
                let _remainBit = BitHandler.readRightBitFromByte(
                    _curPlicyData,
                    bitExtent,
                    bitExtent - offset,
                );
                _curPlicyData = policyData2;
                let _feedBitLen = 2 - (bitExtent - offset);
                let _feedBit = BitHandler.readLeftBitFromByte(_curPlicyData, bitExtent, _feedBitLen);
                msgValue = BitHandler.concatBinaryNumber(_remainBit, _feedBit, _feedBitLen);
                offset = _feedBitLen;
            } else {
                msgValue = BitHandler.getReadMidNumFromByte(_curPlicyData, bitExtent, offset, offset + 2);
                offset += 2;
            }

            if (offset >= bitExtent) {
                _curPlicyData = policyData2;
                offset = 0;
            }

            _MsgHeadMap.set(msgType, msgValue);
        }

        //跳过无效字节
        let slackByteLen = BitHandler.readRightBitFromByte(_curPlicyData, bitExtent, 3);
        for (let i = 0; i < slackByteLen; i++) {
            buffer.readUint8();
        }
        //根据消息字段对应操作类型 进行解析
        _MsgHeadMap.forEach(
            function (opValue: number, key: number) {
                switch (key) {
                    case 0:
                        let U32serverType = buffer.readUint16(); //服务器类型
                        retArray[0] = this.getValueByOp(opValue, 16, U32serverType);
                        break;

                    case 1:
                        let U32serverid = buffer.readUint16(); //服务器ID
                        retArray[1] = this.getValueByOp(opValue, 16, U32serverid);
                        break;

                    case 2:
                        let u16PackLen = buffer.readUint16();
                        retArray[2] = this.getValueByOp(opValue, 16, u16PackLen);
                        break;

                    case 3:
                        let u16Msgid = buffer.readUint16(); //消息id
                        retArray[3] = this.getValueByOp(opValue, 16, u16Msgid);
                        break;

                    case 4:
                        let u32seq = buffer.readUint32();
                        retArray[4] = this.getValueByOp(opValue, 32, u32seq);
                        break;

                    case 5:
                        let U32playerid = buffer.readUint32(); //playerid
                        retArray[5] = this.getValueByOp(opValue, 32, U32playerid);
                        break;

                    case 6:
                        let U32roomid = buffer.readUint32(); //房间号
                        retArray[6] = this.getValueByOp(opValue, 32, U32roomid);
                        break;
                }
            }.bind(this),
        );

        return retArray;
    }

    private onmessage = (msg: any): void => {
        this.lastOnMessageCall = Date.now();

        let burffer = new ByteArray();
        burffer.createBuffer(msg.data);
        burffer.wpos = msg.data.byteLength;

        let policyData1 = burffer.readUint32();
        let policyData2 = burffer.readUint32();
        this._xorValue = policyData2;
        let retHeaderArray: number[] = this.parsePolicyData(burffer, policyData1, policyData2);
        if (retHeaderArray.length < 1) {
            // console.log("Error: onmessage retHeaderArray is null.");
            return;
        }
        let U32serverType = retHeaderArray[0];
        let U32serverid = retHeaderArray[1];
        let u16PackLen = retHeaderArray[2];
        let u16Msgid = retHeaderArray[3];
        let u32seq = retHeaderArray[4];
        let U32playerid = retHeaderArray[5];
        let U32roomid = retHeaderArray[6];

        // console.log("收包:  u16Msgid:" + u16Msgid + "  U32serverid:" + U32serverid + "  U32serverType:" + U32serverType + "  u16PackLen:" + u16PackLen + "  u32seq:" + u32seq + "  U32playerid:" + U32playerid + "  U32roomid:" + U32roomid);

        //！消息体
        let pbbuf = burffer.getbuffer();

        //!是否解密
        let decryptStr: Int8Array;
        let isEncrypt: boolean = this.isEncrypt(U32serverid);

        if (isEncrypt) {
            decryptStr = aesHandler.DecryptBytes(pbbuf);
            // console.log(decryptStr);
        } else {
            // console.log(pbbuf);
        }

        //因为急速、必下消息分发回调都与德州Texas是一样的，都是根据德州Texsa，serverid绑定的。在搜索消息分发回调函数之前，将搜索的serverid设置为Texas的
        if (cv.roomManager.checkGameIsZoom(U32serverid)) {
            U32serverid = cv.Enum.GameId.Texas;
        } else if (
            U32serverid === cv.Enum.GameId.Bet ||
            U32serverid === cv.Enum.GameId.StarSeat ||
            U32serverid === cv.Enum.GameId.Plo
        ) {
            U32serverid = cv.Enum.GameId.Texas;
        }

        //！消息分发
        let value: Map<number, Function> = this.handlers.get(U32serverid);
        if (value) {
            let func = value.get(u16Msgid);
            if (typeof func == 'function') {
                // 测试环境不抛异常便于查找错误
                if (cv.config.GET_DEBUG_MODE() == 1) {
                    func(isEncrypt ? decryptStr : pbbuf, u16Msgid);
                } else {
                    try {
                        func(isEncrypt ? decryptStr : pbbuf, u16Msgid);
                    } catch (e) {
                        console.error('onmessage err:' + e);
                        return;
                    }
                }
            }
        }
    }

    public disconnect(): void {
        this.connectionStatus = CONNECTION_STATUS.STOPPED;
        logging.withTag('WEBSOCKET').info('PKW.NetWork.disconnect called');
        this.close();
    }

    private close(): void {
        logging.withTag('WEBSOCKET').info('PKW.NetWork.close called');
        cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
        if (this.wSocket) {
            this.wSocket.onopen = null;
            this.wSocket.onmessage = null;
            this.wSocket.onerror = null;
            this.wSocket.onclose = null;
            this.wSocket.close();
            this.wSocket = null;
            this.u32seq = 0;
        }
    }

    isConnected(): boolean {
        return this.connectionStatus === CONNECTION_STATUS.CONNECTED;
    }

    isEncrypt(serverId: number): boolean {
        const encryptedGames = cv.dataHandler.getUserData().isEncrypt;

        if (cv.roomManager.checkGameIsZoom(serverId)) {
            if (encryptedGames.some((id) => cv.roomManager.checkGameIsZoom(id))) {
                return true;
            }
        }

        return encryptedGames.includes(serverId);
    }
}
