export class ServerInfo {
    data_server: string = ''; // 数据服
    web_server: string = ''; // web服
    image_server_2: string = ''; // web服(目前用于post上传图片语言等文件)
    image_server: string = ''; // 图片服
    gate_server: string = ''; // 网关(转接go服务器)
    pkt_shop: string = ''; // pkt支付
    invalid: boolean; // 无效域名
    image_server_wpk: string = ''; // wpk 图片域名
    image_server_wpto: string = ''; // wpto 图片域名
}

export class DomainMgr {
    private static _g_instence: DomainMgr;
    // 是否所有域名无法连接，并且有请求百度成功的标记this._isLoginError（说明网络没问题）此标记为true时用于启动去CDN下载域名列表
    private _isLoginError: boolean = false;
    // 登陆服域名数组 只包函（web_server）
    private _loginServerList: string[] = [];
    // 登陆服域名列表索引 轮询标记
    private _loginServerList_index: number = 0;

    // 服务器域名组列表 包函(data_server web_server image_server gate_server)
    private _domain: ServerInfo[] = [];
    // 服务器域名组列表索引，轮询标记
    private _domain_index: number = 0;
    // 服务器域名组列表中是否还有可连接的域名组，每次网关连接成功以后重置
    private _domain_reconnect_num = 0;

    // CND存储文件夹
    private DOWNLOAD_IMAGE_FOLDER: string = 'downloadFile/';
    // CND存储文件
    private CDN_FILE_NAME: string = 'cdnFile';
    // 域名存储文件侠
    private SERVE_FILE_PATH: string = 'ip/';
    // 存储上次登录成功的域名信息文件
    private LASTLOGIN: string = 'lastLogin';
    // 存储本次登录成功下发的域名组文件
    private LOGINDOMAIN: string = 'login';
    // 百度连接
    private _baiduUrl: string = 'https://www.baidu.com/';
    // 上一次连接请求失败的接口名
    private _lastFailServerName: string = '';
    // 解码AES KEY
    private HTTP_AES_KEY: string = '3RLojOn0Gp8AB1iktWY4qf7QmwaMK2hH';
    // 登录下发的cdn名称
    private CDN: string = 'cdn';
    // 在请求热更地址时一起下发的图片默认上传域名
    private defult_imgSever: string = ''; // http://***********:10010/

    public addDomain(ite: any) {
        const re = new ServerInfo();
        re.gate_server = ite.h5;
        // re.data_server = ite.data;
        re.image_server = ite.image_server;
        re.image_server_2 = ite.qiniu2;
        re.web_server = ite.web_server;
        // re.pkt_shop = ite.ucenter;
        re.invalid = false;
        re.image_server_wpk = ite.wpk;
        re.image_server_wpto = ite.wpto;
        this._domain.push(re);
        //console.log("[SDK] ServerInfo: " + JSON.stringify(this._domain[0]))
    }

    public static getInstance(): DomainMgr {
        if (!DomainMgr._g_instence) {
            DomainMgr._g_instence = new DomainMgr();
        }
        return DomainMgr._g_instence;
    }

    public getServerInfo(): ServerInfo {
        if (this._domain_index < 0 || this._domain_index >= this._domain.length) {
            const data: ServerInfo = new ServerInfo();
            data.invalid = true;
            return data;
        }
        return this._domain[this._domain_index];
    }

    public initLoginServer() {}
}
