import { logging } from 'shared';
import { HeartBeat } from '../common/HeartBeat';
import cv from '../cv';
import { ecdhHandler } from '../tools/ecdhHandler';

export class NetWorkManager {
    serverFailCounts: number;
    _isswitchServer: boolean = false;
    _isManualSwitch: boolean = false;
    isInVideoCowBoyScene: boolean = false;
    isInPKFLiveScene: boolean = false;
    _worldHeartBeat: HeartBeat = null;

    private static _g_instance: NetWorkManager = null; // 单例
    public _isLoginFailed: boolean = false; //cowboy_web专用

    public static getInstance(): NetWorkManager {
        if (!this._g_instance) {
            this._g_instance = new NetWorkManager();
            this._g_instance.init();
        }
        return this._g_instance;
    }

    public OnNeedRelogin(error: number) {
        logging.withTag('SDK').info(`[SDK] OnNeedRelogin`);
        cv.MessageCenter.send('onLoginServerError', error);
    }

    public onWorldHeartBeat() {
        console.log('onWorldHeartBeat');
    }

    public init() {
        this._isswitchServer = false;
        this.startWorldHeartBeat();
        // this._worldHeartBeat = new HeartBeat(12,5,this.requestHeartbeat.bind(this), this.OnWorldTimeOut.bind(this), ()=>{})
    }

    // public reconnectByServerFailed() {
    //     cv.netWork.close();
    //     cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
    //     cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
    //     this._worldHeartBeat.stop();
    //     this._gameHeartBeat.stop();
    // }

    // requestHeartbeat():boolean{
    //     return cv.worldNet.requestHeartBeat();
    // }

    // relogin if time out
    public OnWorldTimeOut() {
        logging.withTag('SDK').info('[SDK] OnWorldTimeOut: re login');
        cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
        cv.worldNet.requestLoginServer();
    }

    public Logout(isOnlyClear: boolean = false) {
        //  if ((<any>window).CurrentUserInfo.user.wasUserInDiamondGame)
        //       (<any>window).CurrentUserInfo.user.wasUserInDiamondGame = false;
        // console.log("logout ********");

        this._isswitchServer = false;

        cv.netWork.disconnect();
        this.closeGameConnect();
        // 清除相关数据
        cv.GameDataManager.tGameData.reset(); // 清除游戏数据

        cv.dataHandler.clearData(); // 清除玩家自身数据

        cv.MessageCenter.send('onLogoutScene');
    }

    public closeGameConnect(willReconnect: boolean = false) {
        logging.info('[SDK] closeGameConnect');
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
        cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
        cv.roomManager.resetRoomCache();

        if (!willReconnect) {
            cv.roomManager.reset();
        }
    }

    public OnWorldServerLogin(pSend) {
        ecdhHandler.getInstance().ecdh_init();

        // for switch Line in lobby
        if (cv.config.getCurrentScene() == cv.Enum.SCENE.HALL_SCENE) {
            //解决bug:5784
            //在大厅切换线路，重新登录world服后，服务器会将secretKey清除掉。会导致重新交换key之前，客户端与服务器secretkey不一致。
            //在登录world后，重新交换这个状态设置为false。这样在大厅点切换线路，再点击房间列表，joinRoom的时候就会重新交换密钥（不重新执行ecdh_init()初始化）
            //ecdhHandler.getInstance().ecdh_setNeedGenKeyState(false);
        }

        // if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
        //     // if (cv.dataHandler.getUserData().m_bIsNewRegisted) {
        //     cv.worldNet.requestDeviceInfoReport(cv.Enum.ReportType.REPORT_REGEGIST);
        //     // }
        // }
        //请求banner
        // if (cv.dataHandler.getBannerMapSize() == 0) {
        //     cv.worldNet.BannerRequest();
        // }

        cv.dataHandler.getUserData().isFirstLogin = false;

        //   cv.domainMgr.writeLastLogin();
        cv.domainMgr.initLoginServer();

        //   cc.sys.localStorage.setItem('lastDomain', cv.domainMgr.getServerInfo().gate_server);

        if (cv.roomManager.getCurrentGameID() == cv.Enum.GameId.GameId_Dummy) {
            //  this.requestClubInfo();
        }
    }

    public OnGameServerLogin(pSend) {
        cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = true;
        this.startGameHeartBeat();
        // this.StartGameHeartBeat();
        cv.roomManager.RequestJoinRoom();
        if (this._isswitchServer) {
            this._isswitchServer = false;
        }
    }

    private _gameHeartBeatId: NodeJS.Timeout = null;
    startGameHeartBeat() {
        clearInterval(this._gameHeartBeatId);

        this._gameHeartBeatId = setInterval(() => {
            cv.gameNet.requestHeartBeat();
        }, 8000);
    }

    private _worldHeartBeatId: NodeJS.Timeout = null;
    startWorldHeartBeat() {
        clearInterval(this._worldHeartBeatId);

        this._worldHeartBeatId = setInterval(() => {
            cv.worldNet.requestHeartBeat();
        }, 3000);
    }

    public startGame() {
        logging.info('PKW.NetWorkManager start');
        cv.worldNet.initCommon(cv.Enum.GameId.World);
        cv.gameNet.initCommon(cv.Enum.GameId.Texas);
        cv.dataNet.initCommon(cv.Enum.GameId.Data);

        if (!cv.netWork.isConnected()) {
            cv.dataHandler.getUserData().m_bIsLoginServerSucc = false;
            cv.dataHandler.getUserData().m_bIsLoginGameServerSucc = false;
            cv.netWork.connect();
        }
    }
}
