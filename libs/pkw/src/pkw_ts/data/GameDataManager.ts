// 游戏数据管理类

import cv from '../cv';
import { RoomData } from './RoomData';
import { GameData } from './GameData';
import { CurrencyType } from '../tools/Enum';


export class BarrageCountData {
    public count: number;
    public BarrageId: number;
    public content: string;
}

export class GameDataManager {
    tRoomData: RoomData = null; // 房间数据

    tGameData: GameData = null; // 游戏数据

    bIsAuthMicphone: boolean = false;
    bIsAuthLocation: boolean = false;
    public tappedCurrency: number = 0;
    public danmuCounts: BarrageCountData[] = [];
    private static g_instance: GameDataManager = null; // 单例
    public bIsBuyinPending = false;
    private constructor() {
        // 构造
        this._init();
    }

    static getInstance(): GameDataManager {
        if (!GameDataManager.g_instance) {
            GameDataManager.g_instance = new GameDataManager();
        }
        return GameDataManager.g_instance;
    }

    clearData(): void {
        GameDataManager.g_instance = null;
        GameDataManager.g_instance = GameDataManager.getInstance();
    }

    /**
     * clearBarrageData
     */
    public clearBarrageData() {
        this.danmuCounts = [];
    }
    /**
     * 增加一条弹幕使用数据
     */
    public addBarrageData(data: BarrageCountData) {
        this.danmuCounts.push(data);
    }

    /**
     * 给弹幕数据排序
     */
    public sortBarrageData() {
        this.danmuCounts.sort(this.sortByUserCount.bind(this));
        for (let i = 0; i < this.danmuCounts.length; i++) {
            console.log(
                cv.StringTools.formatC(
                    'index:%d baId:%d  usecount:%d content:%s',
                    i,
                    this.danmuCounts[i].BarrageId,
                    this.danmuCounts[i].count,
                    this.danmuCounts[i].content,
                ),
            );
        }
    }

    /**
     * getBar
     */
    public getBarrageData(): BarrageCountData[] {
        return this.danmuCounts;
    }
    /**
     * name
     */
    public sortByUserCount(a: BarrageCountData, b: BarrageCountData) {
        if (a.count > b.count) {
            return -1;
        } else if (a.count < b.count) {
            return 1;
        } else if (a.count == b.count) {
            if (a.BarrageId < b.BarrageId) {
                return -1;
            } else if (a.BarrageId > b.BarrageId) {
                return 1;
            } else {
                return 0;
            }
        }
    }

    isUSDTable(): boolean {
        return cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE
            ? cv.GameDataManager.tappedCurrency === CurrencyType.USD
            : cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.USD;
    }

    isDiamondTable(): boolean {
        return cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE
            ? cv.GameDataManager.tappedCurrency === CurrencyType.DIAMOND
            : cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.DIAMOND;
    }

    isGoldTable(): boolean {
        return cv.config.getCurrentScene() === cv.Enum.SCENE.HALL_SCENE
            ? cv.GameDataManager.tappedCurrency === CurrencyType.GOLD
            : cv.GameDataManager.tRoomData.pkRoomParam.currencyType === CurrencyType.GOLD;
    }

    private _init(): void {
        // 房间数据
        this.tRoomData = RoomData.getInstance();
    }
}

let gameDataMgr: GameDataManager = null;
export default gameDataMgr = GameDataManager.getInstance();
