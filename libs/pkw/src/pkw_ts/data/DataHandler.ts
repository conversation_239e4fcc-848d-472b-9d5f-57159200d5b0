import { RobotConst } from '../../wpk_robot/common/wpkRobotConst';
import cv from '../cv';
import { ServerButtonType } from '../tools/Enum';
import { userData } from './userData';

export class BannerInfo {
    imageUrl: string = '';
    webViewUrl: string = '';
}

/**
 * 用户数据管理类
 */
export class DataHandler {
    private userData: userData = null;
    private static instance: DataHandler;
    private bannerMapCache = new Map<number, BannerInfo[]>();
    private serverId: ServerButtonType;

    public static getInstance(): DataHandler {
        if (!this.instance) {
            this.instance = new DataHandler();
            this.instance.init();
        }
        return this.instance;
    }

    public init() {
        if (RobotConst.cfg.platform == 'dev') {
            this.serverId = cv.Enum.ServerButtonType.ServerButtonType_ceshifu;
            //this.serverId = 11 // dev env serverID
        } else {
            this.serverId = cv.Enum.ServerButtonType.ServerButtonType_zhenshifu;
            //this.serverId = 12 // prod env serverID
        }
    }

    public getUserData(): userData {
        if (!this.userData) {
            this.userData = userData.getInstance();
        }
        return this.userData;
    }

    addBannerUrl(gameType: number, imgUrl: BannerInfo): void {
        let arr = this.bannerMapCache.get(gameType);
        if (!arr) {
            this.bannerMapCache.set(gameType, [imgUrl]);
        } else {
            arr.push(imgUrl);
        }
    }

    getBannerUrlList(gameType: number): BannerInfo[] {
        return this.bannerMapCache.get(gameType);
    }

    getBannerMapSize(): number {
        return this.bannerMapCache.size;
    }

    clearBanner(): void {
        this.bannerMapCache.clear();
    }

    public clearData(): void {
        // 清理用户数据
        if (this.userData) this.userData = userData.clearData();
        this.init();
        // 清理其他...
    }

    public getServerId(): number {
        return this.serverId;
    }

    public setServerId(severid) {
        this.serverId = severid;
    }
}
