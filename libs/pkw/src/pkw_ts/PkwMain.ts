import path from 'path';
import protobuf from 'protobufjs';

import {
    DelayService,
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    logging,
    MttUserData,
    PkwUserData,
    RoomStartParams,
} from 'shared';

import { pb as world_pb } from '../proto/ws_protocol';

import cv from './cv';
import { DomainMgr } from './network/DomainMgr';
import pkwGame from './pkwGame';
import pkwRoom, { LobbyDataFunction } from './pkwRoom';
import { ecdhHandler } from './tools/ecdhHandler';
import * as ServerErrorCodes from '../commoms/SeverErrorCodeTable.json';

export class PkwMain {
    private static _loginData;

    public static async initScan(proxyUrl?: string) {
        logging.info('PkwMain.initScan');
        cv.initCV(proxyUrl);
        pkwRoom.init();
        await this._loadProto();
    }

    public static async init(
        currencies: string[],
        proxyUrl?: string,
        gameId?: world_pb.GameId,
        gameMode: GameMode = GameMode.NORMAL,
        gameTypeCode: GameType = GameType.NLHE,
    ) {
        cv.initCV(proxyUrl);
        pkwRoom.init();

        pkwGame.init();
        pkwGame.setGameModeCode(GameMode.NORMAL); // Should be NORMAL for all gameModes, even SPLASH, ZOOM and BOMB
        pkwGame.setDelayService(new DelayService(gameMode));

        await this._loadProto();

        pkwRoom.setCurrencies(currencies);
        pkwRoom.setGameId(gameId);
        pkwRoom.setGameTypeCode(gameTypeCode);
    }

    public static addDomain(r) {
        DomainMgr.getInstance().addDomain(r.pkwAuthData);
        this._loginData = r;
    }

    public static pkwScan(
        loginData: PkwUserData,
        jobType: JobType,
        onMessage: LobbyDataFunction,
        errorCB = (_: any) => {},
    ) {
        logging.withTag('SDK').info('pkwScan');
        pkwRoom.setLobbyDataCb(onMessage);
        PkwMain.onWPKLogin(loginData, jobType, errorCB);
    }

    public static pkwPlay(
        loginData: PkwUserData,
        jobType: JobType,
        joinRoomId: number,
        roomParams: RoomStartParams,
        onMessage: JobDataHandler,
        errorCB = (_: any) => {},
    ) {
        logging.withTag('SDK').info('pkwPlay');

        pkwGame.setUpdateProgressCb(onMessage);
        pkwGame.setProfileName(roomParams.profileName);

        pkwRoom.setJoinRoomId(joinRoomId);
        pkwRoom.setRoomParams(roomParams);

        PkwMain.onWPKLogin(loginData, jobType, errorCB);
    }

    public static pkwLogin(
        loginData: PkwUserData,
        jobType: JobType,
        onLogin: (data: MttUserData) => void,
        errorCB = (_: any) => {},
    ) {
        logging.withTag('SDK').info('pkwLogin');
        pkwRoom.setLoginServerCB(onLogin);
        PkwMain.onWPKLogin(loginData, jobType, errorCB);
    }

    static onWPKLogin(loginData: PkwUserData, jobType: JobType, errorCB = (_: any) => {}) {
        this._setLoginUserData(loginData);
        cv.dataHandler.getUserData().deviceInfo = `{"disroot":false,"dmodel":"","dname":"wefans","duuid":"${loginData.deviceId}","dversion":""}`;
        this._md5token();
        this._setDomainData();

        pkwRoom.setPkwUserData(loginData.pkwAuthData.uid, jobType);
        pkwRoom.setErrorCb((error: any) => {
            if (error instanceof Error) {
                errorCB(error);
            } else {
                errorCB(new Error(ServerErrorCodes['ServerErrorCode' + error]?.['-value'] ?? error));
            }
        });

        ecdhHandler.getInstance().ecdh_init();
        cv.netWorkManager.startGame();
    }

    public static async finishGame() {
        return new Promise<void>((resolve) => {
            pkwRoom.leaveRoom(resolve);
        });
    }

    public static closeWebSocket() {
        logging.withTag('WEBSOCKET').info('PKW - Deliberately closing websocket connection...');

        cv.netWork.disconnect();
    }

    /**
     * 加载pb
     */
    private static async _loadProto(): Promise<void> {
        // 设置"pb"字段保持原样输出
        const pb_parse: any = protobuf.parse;
        pb_parse.defaults.keepCase = true;

        // 加载pb协议文件
        const vPBInfo: any[] = [
            { path: path.dirname(__dirname) + '/proto/ws_protocol.proto', type: 'worldPB' },
            { path: path.dirname(__dirname) + '/proto/gs_protocol.proto', type: 'gamePB' },
            { path: path.dirname(__dirname) + '/proto/gate.proto', type: 'gate' },
            { path: path.dirname(__dirname) + '/proto/data.proto', type: 'data' },
        ];

        const loadPromises = vPBInfo.map(async (info) => {
            try {
                const source = await protobuf.load(info.path);
                switch (info.type) {
                    case 'worldPB':
                        cv.worldPB = source;
                        break;
                    case 'gamePB':
                        cv.gamePB = source;
                        break;
                    case 'gate':
                        cv.gatePB = source;
                        break;
                    case 'data':
                        cv.dataPB = source;
                        break;
                    default:
                        break;
                }
            } catch (error: any) {
                logging.error('protobuf load error', error, { path: info.path });
                throw error;
            }
        });
        await Promise.all(loadPromises);
    }

    private static _setDomainData() {
        for (let i = 0; i < this._loginData.pkwAuthData.gate_addr.length; i++) {
            const ite = {
                h5: this._loginData.pkwAuthData.gate_addr[i],
                image_server: this._loginData.pkwAuthData.pkw_file_addr,
                web_server: this._loginData.pkwAuthData.api_addr,
                qiniu2: this._loginData.pkwAuthData.qiniu2,
                wpk: this._loginData.pkwAuthData.avatar_addr,
                wpto: this._loginData.pkwAuthData.wpto,
            };
            cv.domainMgr.addDomain(ite);
        }
    }

    private static _setLoginUserData(loginData: any) {
        const userData = cv.dataHandler.getUserData();

        userData.user_ip = loginData.pkwAuthData.appIP;
        userData.user_token = loginData.pkwAuthData.token;
        userData.u32Uid = loginData.pkwAuthData.uid;
        userData.user_id = loginData.pkwAuthData.uid.toString();
        userData.nick_name = loginData.user.nickname;
        userData.u32Chips = cv.StringTools.times(cv.Number(loginData.amount), 100);
        userData.file_upload_url = loginData.pkwAuthData.pkw_file_addr;
        userData.isallowsimulator = loginData.pkwAuthData.is_allow_simulator;
        userData.isViewWPT = loginData.pkwAuthData.wpt;
        if (loginData.pkwAuthData.client_type) {
            cv.config.SET_CLIENT_TYPE(loginData.pkwAuthData.client_type);
        }

        this._loginData = loginData;
    }

    private static _md5token() {
        const token = cv.dataHandler.getUserData().user_token;
        cv.dataHandler.getUserData().user_token = cv.md5.md5(cv.md5.md5(token));
    }
}
