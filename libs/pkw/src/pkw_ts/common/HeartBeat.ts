enum HeartBeatState {
    Idle = 0,
    Requesting,
    Receiving,
    Received,
}

export class HeartBeat {
    private _intervalSecs: number = 0;
    private _timeOutSecs: number = 0;
    private _requestFunc: () => boolean;
    private _receiveFunc: () => void;
    private _timeOutFunc: () => void;
    private _state: HeartBeatState = HeartBeatState.Idle;

    private intervalId: NodeJS.Timeout | null = null;
    //  private _intervalSecs: number = 1000; // 設定你的間隔時間（毫秒）

    constructor(
        interval: number,
        timeOutSecs: number,
        requestFunc: () => boolean,
        timeOutFunc: () => void,
        receiveFunc: () => void,
    ) {
        this._intervalSecs = interval * 1000;
        this._timeOutSecs = timeOutSecs * 1000;
        this._requestFunc = requestFunc;
        this._timeOutFunc = timeOutFunc;
        this._receiveFunc = receiveFunc;
    }

    private unscheduleAllForTarget(): void {
        if (this.intervalId !== null) {
            clearInterval(this.intervalId);
            this.intervalId = null;
        }
    }

    private enableForTarget(): void {}

    private schedule(callback: () => void, intervalSecs: number): void {
        this.intervalId = setInterval(callback, intervalSecs);
    }

    public stop(): void {
        this._state = HeartBeatState.Idle;
        this.unscheduleAllForTarget();
    }

    public dispose(): void {
        this._requestFunc = null;
        this._timeOutFunc = null;
        this._receiveFunc = null;
    }

    public startHeartBeat(): void {
        this.enableForTarget();
        this.schedule(this._doHeartBeat, this._intervalSecs);
    }

    private _doHeartBeat() {
        this._state = HeartBeatState.Requesting;
        if (this._requestFunc()) {
            this._state = HeartBeatState.Receiving;
            setTimeout(() => {
                this._checkTimeout();
            }, this._timeOutSecs * 1000);
        } else {
            this._onTimeOut();
        }
    }

    public receiveHeartBeat(): void {
        this._state = HeartBeatState.Received;
        if (this._receiveFunc) {
            this._receiveFunc();
        }
    }

    private _checkTimeout(): void {
        if (this._state === HeartBeatState.Receiving) {
            this._onTimeOut();
        }
    }

    private _onTimeOut(): void {
        this._state = HeartBeatState.Idle;
        this._timeOutFunc();
    }
}
