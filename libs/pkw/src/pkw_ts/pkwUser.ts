import { UserStatus } from "shared";

export enum UserActionEnum {
    default = 0,
    login,
    requestLobbyData,
    joinRoom,
    sitDown,
    buyin,
    sitBack,
    buyOut,
    sitOut,
    leaveRoom,
    checkCurrentRoom,
}


export enum GameActionEnum {
    default = 0,
    check,
    fold,
    allin,
    bet,
    call,
}

export class PkwUser {
    pkwUid: number = -1;
    currentRoomId: number = -1;
    currentStatus: UserStatus = UserStatus.default;
    userActions: UserActionEnum[] = [];
    gameActions: GameActionEnum[] = [];
    constructor(pkwUid: number, userActions: UserActionEnum[], gameActions: GameActionEnum[]) {
        this.pkwUid = pkwUid;
        this.userActions = userActions;
        this.gameActions = gameActions;
    }
}
