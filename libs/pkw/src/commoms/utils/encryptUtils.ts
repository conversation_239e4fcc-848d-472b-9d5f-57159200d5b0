import crypto from 'node:crypto';

export function MD5(str: string) {
    return crypto.createHash('md5').update(str).digest('hex').toUpperCase();
}

export function encryptRSA(str: string, pubKey: string): string {
    const buffer = Buffer.from(str, 'utf8');
    const key = `-----BEGIN PUBLIC KEY-----\n${pubKey}\n-----END PUBLIC KEY-----`;
    const encrypted = crypto.publicEncrypt(
        {
            key,
            padding: crypto.constants.RSA_PKCS1_PADDING, // or OAEP if your key expects it
        },
        buffer
    );
    return encrypted.toString('base64');
}

export default {
    MD5,
    encryptRSA,
}
