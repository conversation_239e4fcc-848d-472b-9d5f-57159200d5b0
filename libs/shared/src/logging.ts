import { v7 as uuid } from 'uuid';

const NO_LOG_TAGS = (process.env.NO_LOG_TAGS || '').split(',').map((tag) => tag.trim());

type contextType = {
    platform?: string; // contains appId, used by RL team
    roomId?: any;
    userId?: any;
    tournamentId?: any;
    tag?: string;
    status?: string;
    lastActionTaken?: string;
    lastEventTriggered?: string;
    holeCards?: string;
    stack?: number;
    roundUuid?: string;
    playerName?: string;
    [key: string]: any; // some various data
};

class DummyLogging {
    public info(msg: string, props: any = null) {
        return;
    }

    public warn(msg: string, props: any = null) {
        return;
    }

    public error(msg: string, err: any, props: any = null) {
        return;
    }
}

class Logging {
    private _ctx: contextType = {};

    constructor(ctx?: contextType) {
        if (ctx) {
            this._ctx = ctx;
        }
    }

    public withTag(tag: string) {
        if (NO_LOG_TAGS.includes(tag)) {
            return new DummyLogging();
        }
        return new Logging({ ...this._ctx, tag });
    }

    public init(ctx: any) {
        this._ctx = { ...ctx };
    }

    public setRoomId(value: any) {
        if (this._ctx?.roomId !== value) {
            this.info(`roomId changed: ${this._ctx?.roomId} -> ${value}`);
            this._ctx = { ...this._ctx, roomId: value };
        }
    }

    public setUserId(value: any) {
        this._ctx.userId = value;
    }

    public setTournamentId(value: any) {
        this._ctx.tournamentId = value;
    }

    public setStatus(value: any) {
        this._ctx.status = value;
    }

    public setLastActionTaken(value: any) {
        this._ctx.lastActionTaken = value;
    }

    public setLastEventTriggered(value: any) {
        this._ctx.lastEventTriggered = value;
    }

    public setHoleCards(value: string) {
        this._ctx.holeCards = value;
    }

    public setStack(value: number) {
        this._ctx.stack = value;
    }

    public setPlayerName(value: string) {
        this._ctx.playerName = value;
    }

    public resetRoundValues() {
        const roundUuid = uuid();
        this.info(`NEW ROUND: ${roundUuid}`);
        this._ctx = {
            ...this._ctx,
            roomId: null,
            holeCards: null,
            roundUuid,
            status: null,
            stack: null,
        };
    }

    public info(msg: string, data?: any) {
        // console.log(msg);

        console.info(
            JSON.stringify({
                level: 'info',
                msg,
                data,
                timestamp: new Date().toISOString(),
                ...this._ctx,
            }),
        );
    }

    public warn(msg: string, data?: any) {
        console.warn(
            JSON.stringify({
                level: 'warning',
                msg,
                data,
                timestamp: new Date().toISOString(),
                ...this._ctx,
            }),
        );
    }

    public error(msg: string, error: any, data?: any) {
        console.error(
            JSON.stringify({
                level: 'error',
                msg,
                error: error?.message || String(error),
                data,
                timestamp: new Date().toISOString(),
                ...this._ctx,
            }),
        );
    }
}

export const logging = new Logging();
