import { GAME_PHASE, SHARED_ACTION, GameMode } from './constants';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';

/**
 * Maps action strings from CSV to SHARED_ACTION enums.
 */
const actionNameToAction = {
    check: SHARED_ACTION.CHECK,
    call: SHARED_ACTION.CALL,
    bet: SHARED_ACTION.BET,
    fold: SHARED_ACTION.FOLD,
    raise: SHARED_ACTION.RAISE,
    'all-in': SHARED_ACTION.ALL_IN,
};

/**
 * Maps phase strings from CSV to GAME_PHASE enums.
 */
const phaseToGamePhase = {
    preflop: GAME_PHASE.PREFLOP,
    flop: GAME_PHASE.FLOP,
    turn: GAME_PHASE.TURN,
    river: GAME_PHASE.RIVER,
};

export const timeBucketLimit = {
    [GameMode.ZOOM]: 7,
    [GameMode.SPLASH]: 12,
    [GameMode.NORMAL]: 14,
};

export class DelayService {
    /**
     * Records any action with a list of incremental probabilities
     * based on phases of a game and action time buckets.
     */
    delayData = {} as Record<SHARED_ACTION, Record<GAME_PHASE, number[]>>;
    delayDataPromise: Promise<Record<SHARED_ACTION, Record<GAME_PHASE, number[]>>>;

    public constructor(gameModeCode: GameMode) {
        if (!timeBucketLimit[gameModeCode]) {
            throw new Error(`Unsupported game mode: ${gameModeCode}`);
        }
        this.delayDataPromise = this.initPromise(gameModeCode);
        this.delayDataPromise.then((delayData) => (this.delayData = delayData));
    }

    /**
     * Returns a delay in milliseconds for an action, optionally adjusted by probability.
     * If the action meets certain conditions, returns a "fast" random delay (0ms–1000ms).
     * Otherwise determines a time bucket based on random probability checks against CSV-provided data.
     */
    public calculateDelayMs = (
        gamePhase: GAME_PHASE,
        action: SHARED_ACTION,
        probability?: number,
    ): number => {
        // Look up time buckets for the current action/phase
        const timeBuckets = this.delayData[action]?.[gamePhase];
        const randomVal = Math.random();

        let delayIndex = timeBuckets?.findIndex((val: number) => randomVal < val) ?? -1;
        if (delayIndex === -1) delayIndex = 2;

        const isUncertain =
            (action === SHARED_ACTION.CHECK && !(probability > 0.5)) ||
            (action === SHARED_ACTION.FOLD && !(probability > 0.8));
        if (isUncertain && delayIndex < 1) delayIndex = 1;

        const minimumWait =
            delayIndex > 0 || action === SHARED_ACTION.CHECK || action === SHARED_ACTION.FOLD ? 0 : 0.5;
        const finalDelay = delayIndex + this.uniformRandom(minimumWait, 1);
        return Math.round(finalDelay * 1000);
    };

    /**
     * Returns a uniform random number in [from, to).
     */
    private uniformRandom = (from = 0, to = 1): number => {
        return Math.random() * (to - from) + from;
    };

    /**
     * Reads delay.csv, populates delayData with time buckets, normalizes them, then resolves.
     */
    private async initPromise(gameModeCode: GameMode) {
        const csvGameType = gameModeCode === GameMode.ZOOM ? 'Zoom' : 'Normal';
        const bucketLimit = timeBucketLimit[gameModeCode];

        let data = {} as Record<SHARED_ACTION, Record<GAME_PHASE, number[]>>;
        return new Promise<Record<SHARED_ACTION, Record<GAME_PHASE, number[]>>>((resolve, reject) => {
            fs.createReadStream(path.join(__dirname, 'delay.csv'))
                .pipe(csv())
                .on('data', (row) => {
                    if (
                        row.straddle !== 'false' ||
                        row.gamemode !== 'NL' ||
                        row.game_type !== csvGameType ||
                        row.action_time_bucket.startsWith('over')
                    ) {
                        return;
                    }
                    const action = actionNameToAction[row.action];
                    const phase = phaseToGamePhase[row.turns];
                    if (action === undefined || phase === undefined) return;

                    const bucketIndex = parseInt(row.action_time_bucket, 10);
                    if (bucketIndex >= bucketLimit) return;
                    const percent = parseFloat(row.percent_events) / 100;

                    data[action] ??= {};
                    data[action][phase] ??= Array();
                    data[action][phase][bucketIndex] = percent;
                })
                .on('end', () => {
                    Object.values(data).forEach((phases) =>
                        Object.values(phases).forEach((buckets) => {
                            for (let i = 1; i < buckets.length; i++) {
                                buckets[i] += buckets[i - 1];
                            }
                        }),
                    );
                    resolve(data);
                })
                .on('error', reject);
        });
    };

    public getDelayData = async () => this.delayDataPromise;
}
