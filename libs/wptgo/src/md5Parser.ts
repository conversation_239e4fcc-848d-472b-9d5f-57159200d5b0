import { DataHandler } from './DataHandler';
import { aesHand<PERSON> } from './plugg/aesHandler';
import { MD5 } from './utils/mdR5';

export class MD5Parser {
    private static readonly KEY: string = '@lnFi8<eIKYazt:$_;MX9T/d(gk[JW3{Upcw'.substring(0, 32);

    static md5token() {
        let token = DataHandler.getInstance().getUserToken();
        token = aesHandler.DecryptBase64(token, MD5Parser.KEY);
        DataHandler.getInstance().setUserToken(MD5.getInstance().md5(MD5.getInstance().md5(token)));

        token = DataHandler.getInstance().getSharedPlayerToken();
        DataHandler.getInstance().setSharedPlayerToken(MD5.getInstance().md5(MD5.getInstance().md5(token || '')));
    }

    static hashToken(token: string): string {
        return MD5.getInstance().md5(MD5.getInstance().md5(aesHandler.DecryptBase64(token, MD5Parser.KEY)));
    }
}
