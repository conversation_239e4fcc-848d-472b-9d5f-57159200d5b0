export class MD5 {
    public static g_instance: MD5;

    public static getInstance(): MD5 {
        if (!MD5.g_instance) {
            MD5.g_instance = new MD5();
        }
        return MD5.g_instance;
    }

    private getSortSign(): string {
        const clientSaltKey = 'PlfBCVcwepsPSGkE&$%adA#$!!E@JK23';
        const day = 32;
        const len = clientSaltKey.length;
        const encryptArr1: number[] = [];
        const encryptArr2: number[] = [];
        const encryptArr3: number[] = [];
        let tmp = 0;

        for (let i = 0; i < len; i++) {
            encryptArr1.push(clientSaltKey.charCodeAt(i));
        }

        for (let i = 0; i < len; i++) {
            if (i % 2 !== 0) {
                encryptArr2.push(encryptArr1[i] - i * 2 + day);
            } else {
                encryptArr2.push(encryptArr1[i]);
            }
        }

        for (let i = 0; i < len; i++) {
            tmp = encryptArr2[i];
            tmp ^= i % 3;
            tmp >>= 3;
            if (i % 3 === 0) {
                tmp <<= 2;
            } else {
                tmp ^= encryptArr2[i];
            }
            encryptArr3.push(tmp);
        }
        let cryptBefore = '';
        const len2 = encryptArr3.length;
        for (let i = 0; i < len2; i++) {
            // console.log("encrypt_arr3::" + encrypt_arr3[i]);
            cryptBefore += encryptArr3[i];
        }
        // console.log("crypt_before::" + crypt_before);
        const kMd5String = this.md5(cryptBefore);
        // console.log("kMd5String::" + kMd5String);
        return kMd5String;
    }

    public CreateSign(kData: string): string {
        const kSaltSign = this.getSortSign();
        const kSign = kSaltSign + kData + kSaltSign;

        const Md5String = this.md5(kSign);
        return Md5String;
    }

    private utf8(wide: string): string {
        let c: number;
        let s: number;
        let enc = '';
        let i = 0;
        while (i < wide.length) {
            c = wide.charCodeAt(i++);
            // handle UTF-16 surrogates
            if (c >= 0xdc00 && c < 0xe000) {
                // eslint-disable-next-line no-continue
                continue;
            }
            if (c >= 0xd800 && c < 0xdc00) {
                if (i >= wide.length) {
                    // eslint-disable-next-line no-continue
                    continue;
                }
                s = wide.charCodeAt(i++);
                if (s < 0xdc00 || c >= 0xde00) {
                    // eslint-disable-next-line no-continue
                    continue;
                }
                c = ((c - 0xd800) << 10) + (s - 0xdc00) + 0x10000;
            }
            // output value
            if (c < 0x80) {
                enc += String.fromCharCode(c);
            } else if (c < 0x800) {
                enc += String.fromCharCode(0xc0 + (c >> 6), 0x80 + (c & 0x3f));
            } else if (c < 0x10000) {
                enc += String.fromCharCode(0xe0 + (c >> 12), 0x80 + ((c >> 6) & 0x3f), 0x80 + (c & 0x3f));
            } else {
                enc += String.fromCharCode(
                    0xf0 + (c >> 18),
                    0x80 + ((c >> 12) & 0x3f),
                    0x80 + ((c >> 6) & 0x3f),
                    0x80 + (c & 0x3f)
                );
            }
        }
        return enc;
    }

    private toHex(n: number): string {
        const hexchars = '0123456789ABCDEF';
        return hexchars.charAt(n >> 4) + hexchars.charAt(n & 0xf);
    }

    private encodeURIComponentNew(input: string): string {
        const okURIchars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-';
        const s = this.utf8(input);
        let enc = '';
        for (let i = 0; i < s.length; i++) {
            if (okURIchars.indexOf(s.charAt(i)) === -1) {
                enc += '%' + this.toHex(s.charCodeAt(i));
            } else {
                enc += s.charAt(i);
            }
        }
        return enc;
    }

    public md5(inputString: string): string {
        const x: number[] = [];
        let k = 0;
        let AA = 0;
        let BB = 0;
        let CC = 0;
        let DD = 0;
        let a = 0;
        let b = 0;
        let c = 0;
        let d = 0;
        const S11 = 7;
        const S12 = 12;
        const S13 = 17;
        const S14 = 22;
        const S21 = 5;
        const S22 = 9;
        const S23 = 14;
        const S24 = 20;
        const S31 = 4;
        const S32 = 11;
        const S33 = 16;
        const S34 = 23;
        const S41 = 6;
        const S42 = 10;
        const S43 = 15;
        const S44 = 21;
        const encodedString = this.Utf8Encode(inputString);
        x.push(...this.ConvertToWordArray(encodedString));
        a = 0x67452301;
        b = 0xefcdab89;
        c = 0x98badcfe;
        d = 0x10325476;
        for (k = 0; k < x.length; k += 16) {
            AA = a;
            BB = b;
            CC = c;
            DD = d;
            a = this.FF(a, b, c, d, x[k + 0], S11, 0xd76aa478);
            d = this.FF(d, a, b, c, x[k + 1], S12, 0xe8c7b756);
            c = this.FF(c, d, a, b, x[k + 2], S13, 0x242070db);
            b = this.FF(b, c, d, a, x[k + 3], S14, 0xc1bdceee);
            a = this.FF(a, b, c, d, x[k + 4], S11, 0xf57c0faf);
            d = this.FF(d, a, b, c, x[k + 5], S12, 0x4787c62a);
            c = this.FF(c, d, a, b, x[k + 6], S13, 0xa8304613);
            b = this.FF(b, c, d, a, x[k + 7], S14, 0xfd469501);
            a = this.FF(a, b, c, d, x[k + 8], S11, 0x698098d8);
            d = this.FF(d, a, b, c, x[k + 9], S12, 0x8b44f7af);
            c = this.FF(c, d, a, b, x[k + 10], S13, 0xffff5bb1);
            b = this.FF(b, c, d, a, x[k + 11], S14, 0x895cd7be);
            a = this.FF(a, b, c, d, x[k + 12], S11, 0x6b901122);
            d = this.FF(d, a, b, c, x[k + 13], S12, 0xfd987193);
            c = this.FF(c, d, a, b, x[k + 14], S13, 0xa679438e);
            b = this.FF(b, c, d, a, x[k + 15], S14, 0x49b40821);
            a = this.GG(a, b, c, d, x[k + 1], S21, 0xf61e2562);
            d = this.GG(d, a, b, c, x[k + 6], S22, 0xc040b340);
            c = this.GG(c, d, a, b, x[k + 11], S23, 0x265e5a51);
            b = this.GG(b, c, d, a, x[k + 0], S24, 0xe9b6c7aa);
            a = this.GG(a, b, c, d, x[k + 5], S21, 0xd62f105d);
            d = this.GG(d, a, b, c, x[k + 10], S22, 0x2441453);
            c = this.GG(c, d, a, b, x[k + 15], S23, 0xd8a1e681);
            b = this.GG(b, c, d, a, x[k + 4], S24, 0xe7d3fbc8);
            a = this.GG(a, b, c, d, x[k + 9], S21, 0x21e1cde6);
            d = this.GG(d, a, b, c, x[k + 14], S22, 0xc33707d6);
            c = this.GG(c, d, a, b, x[k + 3], S23, 0xf4d50d87);
            b = this.GG(b, c, d, a, x[k + 8], S24, 0x455a14ed);
            a = this.GG(a, b, c, d, x[k + 13], S21, 0xa9e3e905);
            d = this.GG(d, a, b, c, x[k + 2], S22, 0xfcefa3f8);
            c = this.GG(c, d, a, b, x[k + 7], S23, 0x676f02d9);
            b = this.GG(b, c, d, a, x[k + 12], S24, 0x8d2a4c8a);
            a = this.HH(a, b, c, d, x[k + 5], S31, 0xfffa3942);
            d = this.HH(d, a, b, c, x[k + 8], S32, 0x8771f681);
            c = this.HH(c, d, a, b, x[k + 11], S33, 0x6d9d6122);
            b = this.HH(b, c, d, a, x[k + 14], S34, 0xfde5380c);
            a = this.HH(a, b, c, d, x[k + 1], S31, 0xa4beea44);
            d = this.HH(d, a, b, c, x[k + 4], S32, 0x4bdecfa9);
            c = this.HH(c, d, a, b, x[k + 7], S33, 0xf6bb4b60);
            b = this.HH(b, c, d, a, x[k + 10], S34, 0xbebfbc70);
            a = this.HH(a, b, c, d, x[k + 13], S31, 0x289b7ec6);
            d = this.HH(d, a, b, c, x[k + 0], S32, 0xeaa127fa);
            c = this.HH(c, d, a, b, x[k + 3], S33, 0xd4ef3085);
            b = this.HH(b, c, d, a, x[k + 6], S34, 0x4881d05);
            a = this.HH(a, b, c, d, x[k + 9], S31, 0xd9d4d039);
            d = this.HH(d, a, b, c, x[k + 12], S32, 0xe6db99e5);
            c = this.HH(c, d, a, b, x[k + 15], S33, 0x1fa27cf8);
            b = this.HH(b, c, d, a, x[k + 2], S34, 0xc4ac5665);
            a = this.II(a, b, c, d, x[k + 0], S41, 0xf4292244);
            d = this.II(d, a, b, c, x[k + 7], S42, 0x432aff97);
            c = this.II(c, d, a, b, x[k + 14], S43, 0xab9423a7);
            b = this.II(b, c, d, a, x[k + 5], S44, 0xfc93a039);
            a = this.II(a, b, c, d, x[k + 12], S41, 0x655b59c3);
            d = this.II(d, a, b, c, x[k + 3], S42, 0x8f0ccc92);
            c = this.II(c, d, a, b, x[k + 10], S43, 0xffeff47d);
            b = this.II(b, c, d, a, x[k + 1], S44, 0x85845dd1);
            a = this.II(a, b, c, d, x[k + 8], S41, 0x6fa87e4f);
            d = this.II(d, a, b, c, x[k + 15], S42, 0xfe2ce6e0);
            c = this.II(c, d, a, b, x[k + 6], S43, 0xa3014314);
            b = this.II(b, c, d, a, x[k + 13], S44, 0x4e0811a1);
            a = this.II(a, b, c, d, x[k + 4], S41, 0xf7537e82);
            d = this.II(d, a, b, c, x[k + 11], S42, 0xbd3af235);
            c = this.II(c, d, a, b, x[k + 2], S43, 0x2ad7d2bb);
            b = this.II(b, c, d, a, x[k + 9], S44, 0xeb86d391);
            a = this.AddUnsigned(a, AA);
            b = this.AddUnsigned(b, BB);
            c = this.AddUnsigned(c, CC);
            d = this.AddUnsigned(d, DD);
        }
        const temp = this.WordToHex(a) + this.WordToHex(b) + this.WordToHex(c) + this.WordToHex(d);
        return temp;
        // return temp.toUpperCase(); // 大写
    }

    private RotateLeft(lValue: number, iShiftBits: number): number {
        return (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
    }

    private AddUnsigned(lX: number, lY: number): number {
        const lX8 = lX & 0x80000000;
        const lY8 = lY & 0x80000000;
        const lX4 = lX & 0x40000000;
        const lY4 = lY & 0x40000000;
        const lResult = (lX & 0x3fffffff) + (lY & 0x3fffffff);
        if (lX4 & lY4) {
            return lResult ^ 0x80000000 ^ lX8 ^ lY8;
        }
        if (lX4 | lY4) {
            if (lResult & 0x40000000) {
                return lResult ^ 0xc0000000 ^ lX8 ^ lY8;
            }
            return lResult ^ 0x40000000 ^ lX8 ^ lY8;
        }
        return lResult ^ lX8 ^ lY8;
    }

    private F(x: number, y: number, z: number): number {
        return (x & y) | (~x & z);
    }

    private G(x: number, y: number, z: number): number {
        return (x & z) | (y & ~z);
    }

    private H(x: number, y: number, z: number): number {
        return x ^ y ^ z;
    }

    private I(x: number, y: number, z: number): number {
        return y ^ (x | ~z);
    }

    private FF(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
        const result = this.AddUnsigned(a, this.AddUnsigned(this.AddUnsigned(this.F(b, c, d), x), ac));
        return this.AddUnsigned(this.RotateLeft(result, s), b);
    }

    private GG(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
        const result = this.AddUnsigned(a, this.AddUnsigned(this.AddUnsigned(this.G(b, c, d), x), ac));
        return this.AddUnsigned(this.RotateLeft(result, s), b);
    }

    private HH(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
        const result = this.AddUnsigned(a, this.AddUnsigned(this.AddUnsigned(this.H(b, c, d), x), ac));
        return this.AddUnsigned(this.RotateLeft(result, s), b);
    }

    private II(a: number, b: number, c: number, d: number, x: number, s: number, ac: number): number {
        const result = this.AddUnsigned(a, this.AddUnsigned(this.AddUnsigned(this.I(b, c, d), x), ac));
        return this.AddUnsigned(this.RotateLeft(result, s), b);
    }

    private ConvertToWordArray(string: string): number[] {
        let lWordCount: number;
        const lMessageLength = string.length;
        const lNumberOfWords_temp1 = lMessageLength + 8;
        const lNumberOfWords_temp2 = (lNumberOfWords_temp1 - (lNumberOfWords_temp1 % 64)) / 64;
        const lNumberOfWords = (lNumberOfWords_temp2 + 1) * 16;
        const lWordArray = new Array(lNumberOfWords - 1);
        let lBytePosition = 0;
        let lByteCount = 0;
        while (lByteCount < lMessageLength) {
            lWordCount = (lByteCount - (lByteCount % 4)) / 4;
            lBytePosition = (lByteCount % 4) * 8;
            lWordArray[lWordCount] |= string.charCodeAt(lByteCount) << lBytePosition;
            lByteCount++;
        }
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] |= 0x80 << lBytePosition;
        lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
        lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
        return lWordArray;
    }

    private WordToHex(lValue: number): string {
        let wordToHexValue = '';
        let wordToHexValueTemp = '';
        let lByte: number;
        for (let lCount = 0; lCount <= 3; lCount++) {
            lByte = (lValue >>> (lCount * 8)) & 255;
            wordToHexValueTemp = '0' + lByte.toString(16);
            wordToHexValue += wordToHexValueTemp.substr(wordToHexValueTemp.length - 2, 2);
        }
        return wordToHexValue;
    }

    private Utf8Encode(string: string): string {
        let utftext = '';
        for (let n = 0; n < string.length; n++) {
            const c = string.charCodeAt(n);
            if (c < 128) {
                utftext += String.fromCharCode(c);
            } else if (c > 127 && c < 2048) {
                utftext += String.fromCharCode((c >> 6) | 192);
                utftext += String.fromCharCode((c & 63) | 128);
            } else {
                utftext += String.fromCharCode((c >> 12) | 224);
                utftext += String.fromCharCode(((c >> 6) & 63) | 128);
                utftext += String.fromCharCode((c & 63) | 128);
            }
        }
        return utftext;
    }

    private writeUTF(str: string, isGetBytes: boolean): number[] {
        const back: number[] = [];
        let byteSize = 0;
        for (let i = 0; i < str.length; i++) {
            const code = str.charCodeAt(i);
            if (code >= 0x00 && code <= 0x7f) {
                byteSize += 1;
                back.push(code);
            } else if (code >= 0x80 && code <= 0x7ff) {
                byteSize += 2;
                back.push(192 | (31 & (code >> 6)));
                back.push(128 | (63 & code));
            } else if ((code >= 0x800 && code <= 0xd7ff) || (code >= 0xe000 && code <= 0xffff)) {
                byteSize += 3;
                back.push(224 | (15 & (code >> 12)));
                back.push(128 | (63 & (code >> 6)));
                back.push(128 | (63 & code));
            }
        }
        for (let i = 0; i < back.length; i++) {
            back[i] &= 0xff;
        }
        if (isGetBytes) {
            return back;
        }
        if (byteSize <= 0xff) {
            return [0, byteSize].concat(back);
        }
        return [byteSize >> 8, byteSize & 0xff].concat(back);
    }
}
