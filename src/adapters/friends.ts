import { <PERSON><PERSON><PERSON>, <PERSON>Data<PERSON><PERSON><PERSON>, <PERSON>Type, PlatformUserData } from "shared";
import { GamePlatformAdapter } from "../models/model";
import { CreateFriendsRoomParams, FriendsMain } from "friends";

export const FriendsAdapter: GamePlatformAdapter = {
    async init(proxyUrl?: string): Promise<void> {
        const urlConfig = {
            wpkHttpURL: process.env.ROBOT_CONFIG_WPK_URL!,
            wpkGameWsURL: process.env.ROBOT_CONFIG_WPK_URL!.replace('http', 'ws'), // tmp for development
        };
		FriendsMain.init(urlConfig, proxyUrl);
    },
    async start(user: PlatformUserData, jobType: JobType, roomId: number, params: JobData, onMessage: JobDataHandler): Promise<void> {
        await FriendsMain.run(
            user,
            jobType,
            roomId,
            params.clubId,
            params.profileName,
            onMessage,
            params.createRoomParams as Create<PERSON>riendsRoomParams,
        );
    },
    async stop(_: string): Promise<void> {
        setTimeout(() => {
            throw new Error("Friends adapter stop timeout reached, forcibly stopping the game.");
        }, 10_000);
        await FriendsMain.stop();
    },
    isMtt: () => false,

};
