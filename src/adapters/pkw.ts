import { UnrecoverableError } from 'bullmq';
import {
    GameMode,
    GameType,
    JobDataHandler,
    JobType,
    JobData,
    logging,
    RoomStartParams,
    PkwUserData,
    TableData
} from 'shared';
import { CurrencyType } from '../types';
import { PkwMain } from 'pkw';
import { GamePlatformAdapter } from '../models/model';

class PkwAdapterClass implements GamePlatformAdapter {
    constructor(
        private readonly currencies: CurrencyType[],
        private readonly gameId: number = 2,
        private readonly gameMode: GameMode = GameMode.NORMAL,
        private readonly gameType: GameType = GameType.NLHE,
    ) {}

    async init(proxyUrl?: string): Promise<void> {
        await PkwMain.init(
            this.currencies,
            proxyUrl,
            this.gameId,
            this.gameMode,
            this.gameType,
        );
    }

    async start(userData: PkwUserData, jobType: JobType, tableId: number, params: JobData, onMessage: JobDataHandler): Promise<void> {
        if (jobType !== JobType.SCAN && jobType !== JobType.PLAY) {
            throw new UnrecoverableError(`Unsupported game platform action: ${jobType}`);
        }

        if (jobType === JobType.SCAN) {
            return new Promise((_, reject) => {
                PkwMain.pkwScan(userData, jobType, (tables: TableData[]) => {
                    onMessage({ tables: tables.map(t => ({ ...t, appId: params.appId })) });
                }, reject);
            });
        }

        const botParams: RoomStartParams = {
            buyInMultiplier: params.buyInMultiplier ?? 100,
            rebuyEnabled: params.rebuyEnabled ?? true,
            rebuyThreshold: params.rebuyThreshold ?? 50,
            withdrawAmount: params.withdrawAmount ?? 0,
            withdrawThreshold: params.withdrawThreshold ?? 0,
            profileName: params.profileName,
        };
        return new Promise((_, reject) => {
            PkwMain.pkwPlay(userData, jobType, tableId, botParams, onMessage, reject);
        });
    }

    async stop(jobType: JobType): Promise<void> {
        logging.info('[PKW Adapter] stop', jobType);
        if (jobType == JobType.PLAY) {
            await PkwMain.finishGame();
        }
    }

    isMtt(): boolean {
        return false;
    }
}

export const CashAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD]);
export const DiamondAdapter = new PkwAdapterClass([CurrencyType.DIAMOND]);
export const SplashAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD], 60, GameMode.SPLASH);
export const SplashDiamondAdapter = new PkwAdapterClass([CurrencyType.DIAMOND], 60, GameMode.SPLASH);
export const ZoomAdapter = new PkwAdapterClass([CurrencyType.USD, CurrencyType.GOLD], 40, GameMode.ZOOM);
export const ShortDeckAdapter = new PkwAdapterClass(
    [CurrencyType.USD, CurrencyType.GOLD],
    undefined,
    GameMode.NORMAL,
    GameType.SHORTDECK,
);
