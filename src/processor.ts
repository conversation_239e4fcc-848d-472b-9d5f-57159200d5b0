import { SandboxedJob, UnrecoverableError } from 'bullmq';
import { GamePlatformAdapter } from './models/model';
import process from 'node:process';
import { getUserAccount, initMongoDBConnection } from './mongo';
import redis from './redis';
import { version } from '../package.json';
import Redlock from 'redlock';
import { parentPort, workerData } from 'node:worker_threads';
import {
    logging,
    JobType,
    JobUpdateData,
    JobData,
    PlayerStatus,
    PlatformType,
    UserData,
    AppType,
} from 'shared';
import { PlatformAwareGameEntrypoint } from './entrypoint';
import { PlatformStrategy } from './platforms/platform-strategy';

const redlock = new Redlock([redis.connection], {
    retryCount: -1,
    retryDelay: 200,
    retryJitter: 50,
});

const locks: { [key: string]: number } = {
    [`worker-start-job-${AppType.MTT}-${JobType.PLAY}`]: 5000,
    [`worker-start-job-${AppType.MTT}-${JobType.CHECK}`]: 3000,
    [`worker-start-job-${AppType.R4}-${JobType.PLAY}`]: 5000,
    [`worker-start-job-${AppType.R4}-${JobType.CHECK}`]: 3000,
};

const getUserData = async (playerId: string | undefined): Promise<UserData> => {
    const userInformation = await getUserAccount(playerId);
    if (!userInformation) {
        throw new UnrecoverableError(`User not found: ${playerId}`);
    }

    return {
        ...userInformation,
        countryCode: userInformation.countryCode ?? '',
        phoneNum: userInformation.phoneNum ?? '',
        platformId: userInformation.platformId ?? PlatformType.WPK,
    };
};

const loadGameAdapter = async (appId: AppType): Promise<GamePlatformAdapter> => {
    switch (appId) {
        case AppType.CASH:
            return (await import('./adapters/pkw')).CashAdapter;
        case AppType.DIAMOND:
            return (await import('./adapters/pkw')).DiamondAdapter;
        case AppType.MTT:
            return (await import('./adapters/mtt')).MttAdapter;
        case AppType.R4:
            return (await import('./adapters/rmtt')).RMttAdapter;
        case AppType.SPLASH:
            return (await import('./adapters/pkw')).SplashAdapter;
        case AppType.SPLASH_DIAMOND:
            return (await import('./adapters/pkw')).SplashDiamondAdapter;
        case AppType.ZOOM:
            return (await import('./adapters/pkw')).ZoomAdapter;
        case AppType.SHORTDECK:
            return (await import('./adapters/pkw')).ShortDeckAdapter;
        case AppType.FRIENDS:
            return (await import('./adapters/friends')).FriendsAdapter;
        default:
            throw new UnrecoverableError(`Unknown app type: ${appId}`);
    }
};

const loadPlatformStrategy = async (platformId: PlatformType): Promise<PlatformStrategy> => {
    switch (platformId) {
        case PlatformType.RWPK:
            return (await import('./platforms/rwpk')).RWpkPlatform;
        case PlatformType.WPK:
            return (await import('./platforms/wpk')).WpkPlatform;
        case PlatformType.WPTGO:
            return (await import('./platforms/wptgo')).WptgoPlatform;
        default:
            throw new UnrecoverableError(`Unknown platform type: ${platformId}`);
    }
};

function updateProgress(data: JobUpdateData) {
    const dataString = JSON.stringify(data);
    logging
        .withTag('JOB_PROGRESS')
        .info('Updating job progress', dataString.length > 1000 ? `${dataString.slice(0, 1000)}...` : data);

    parentPort?.postMessage({
        updatedAt: Date.now(),
        data,
    });
}

interface WorkerData extends SandboxedJob {
    id: string;
    name: JobType;
    data: JobData;
}

(async () => {
    const job = workerData as WorkerData;

    const jobType = job.name;
    if (!Object.values(JobType).includes(jobType)) {
        throw new UnrecoverableError(`Unknown job type: ${jobType}`);
    }
    const jobData = job.data;

    let gameAdapter: GamePlatformAdapter;
    if (jobType === JobType.SCAN && jobData.appIds && jobData.appIds?.length > 0) {
        gameAdapter = (await import('./adapters/pkwScan')).PkwScanAdapter;
    } else {
        gameAdapter = await loadGameAdapter(jobData.appId);
    }

    await initMongoDBConnection();
    const userData: UserData = await getUserData(jobData.playerId);

    const platformStrategy: PlatformStrategy = await loadPlatformStrategy(userData.platformId);

    const entrypoint = new PlatformAwareGameEntrypoint(platformStrategy, gameAdapter);

    logging
        .withTag('JOB_PROGRESS')
        .info(`Starting ${AppType[jobData.appId]} - ${PlatformType[userData.platformId]} adapter`);

    // acquire lock to prevent multiple workers from starting at the same time
    const lockKey = `worker-start-job-${jobData.appId}-${jobType}`;
    if (locks[lockKey]) {
        await redlock.acquire([lockKey], locks[lockKey]);
    }

    // eslint-disable-next-line no-async-promise-executor
    const exitCode: number = await new Promise(async (resolve, reject) => {
        const jobMonitor = setInterval(async () => {
            const updatedJob = await redis.workerQueue.getJob(job.id!);
            if (updatedJob?.data?.shouldStop) {
                clearInterval(jobMonitor);
                logging
                    .withTag('JOB_PROGRESS')
                    .info(`Exiting job: ${jobType} ID: ${job.id}, reason: ${updatedJob.data.stopReason}`);
                updateProgress({ status: PlayerStatus.STOPPING });

                setTimeout(() => {
                    logging.withTag('JOB_PROGRESS').warn(`Forcing job exit after timeout ID: ${job.id}`);
                    resolve(0);
                }, 300000);

                await entrypoint.stop(jobType);
                updateProgress({ status: PlayerStatus.STOPPED });
                resolve(0);
            }
        }, 5000);

        parentPort?.on('message', async (msg) => {
            // close scan job only, and let other jobs to finish
            if (msg?.type === 'closing' && jobType === JobType.SCAN) {
                logging.withTag('JOB_PROGRESS').info(`Closing job: ${jobType} ID: ${job.id}`);
                clearInterval(jobMonitor);
                await entrypoint.stop(jobType);
                resolve(0);
            }
        });

        logging.init({
            jobId: job.id,
            platform: jobData.appId,
            version,
        });

        await entrypoint.init(jobData.proxyUrl);

        updateProgress({ status: PlayerStatus.INITIALIZED });

        const tableId = Number(jobData.tableId ?? -1);
        if (isNaN(tableId)) {
            throw new UnrecoverableError(`Invalid tableId: ${jobData.tableId}`);
        }
        logging
            .withTag('JOB_PROGRESS')
            .info(
                `Starting job: ${jobType} ID: ${job.id} for user: ${userData.username} on worker version: ${version}`,
            );
        try {
            await entrypoint.start(userData, jobType, tableId, { ...userData, ...jobData }, updateProgress);
            resolve(0);
        } catch (error) {
            updateProgress({ status: PlayerStatus.ERROR });
            reject(error);
        }
    });

    process.exit(exitCode);
})();
