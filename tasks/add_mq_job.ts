import { AppType, JobType } from 'shared';
import redis from '../src/redis';

// 1. Input an appId first
const appId = 81;

// 2. Then input a type play or scan
const type = JobType.SCAN;

// 3. Select player for this appId with sufficient balance
const playerId =
    // '8e712a21-98ed-4b95-9669-e19d1dd49723';
    '42a48bf9-1db0-4d57-beda-362931296717';

// 4. Select tableId if for play
const tableId = '';

const jobId = `${type}:${appId}:${playerId}`; // OLD AWS STG// const playerId = '36529723-f845-4d4d-b32c-3b6b29739f5d'; // Fungamer DEV

async function addJob() {
    const existingJobs = await redis.workerQueue.getJobs();

    // Find and remove existing job with jobId if it exists
    const existingJob = existingJobs.find((job) => job?.id === jobId);
    if (existingJob) {
        console.log('Removing existing job:', existingJob.id);
        await existingJob.remove();
    }

    const data = {
        appId,
        appIds: [AppType.DIAMOND, AppType.ZOOM],
        tableId,
        playerId,
        shouldStop: false,
        type,
        launchId: null,
        proxyUrl: null,
    };

    const job = await redis.workerQueue.add(type, data, { jobId, delay: 3000 });
    console.log('New Job added:', job.id, job.data, job.opts);
}
//
addJob()
    .catch(console.error)
    .finally(() => process.exit(0));
